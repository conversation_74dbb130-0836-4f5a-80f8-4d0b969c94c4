<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume - <PERSON></title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.4;
            margin: 30px;
            background-color: #f9f9f9;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 40px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            border-radius: 5px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #8B0000;
            padding-bottom: 20px;
        }
        .name {
            font-size: 32px;
            font-weight: bold;
            color: #8B0000;
            margin-bottom: 10px;
        }
        .contact-info {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }
        .section {
            margin-bottom: 25px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #8B0000;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .education-item, .experience-item {
            margin-bottom: 15px;
        }
        .institution {
            font-weight: bold;
            font-size: 16px;
            color: #333;
        }
        .degree {
            font-style: italic;
            color: #666;
            margin-bottom: 5px;
        }
        .date-location {
            font-size: 14px;
            color: #888;
            margin-bottom: 8px;
        }
        .gpa {
            font-weight: bold;
            color: #8B0000;
        }
        .job-title {
            font-weight: bold;
            font-size: 16px;
            color: #333;
        }
        .company {
            font-style: italic;
            color: #666;
        }
        .description {
            margin-top: 8px;
            margin-left: 20px;
        }
        .description li {
            margin-bottom: 5px;
            font-size: 14px;
        }
        .skills-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .skill-category {
            margin-bottom: 15px;
        }
        .skill-category-title {
            font-weight: bold;
            color: #8B0000;
            margin-bottom: 5px;
        }
        .skill-list {
            font-size: 14px;
            line-height: 1.6;
        }
        .awards-list {
            font-size: 14px;
            line-height: 1.8;
        }
        .awards-list li {
            margin-bottom: 5px;
        }
        @media print {
            body {
                background-color: white;
                margin: 0;
            }
            .container {
                box-shadow: none;
                margin: 0;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="name">Jack Brown</div>
            <div class="contact-info">
                1234 University Drive, Tempe, AZ 85281<br>
                Phone: (************* | Email: <EMAIL><br>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Education</div>
            <div class="education-item">
                <div class="institution">Arizona State University</div>
                <div class="degree">Bachelor of Science in Biology (Pre-Health Track)</div>
                <div class="date-location">Expected Graduation: May 2027 | Tempe, AZ</div>
                <div class="gpa">Current GPA: 3.45/4.0</div>
                <div class="description">
                    <strong>Relevant Coursework:</strong> General Biology I & II, Organic Chemistry I & II, Human Anatomy & Physiology, Genetics, Microbiology, Statistics for Life Sciences
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Volunteer Experience</div>
            <div class="experience-item">
                <div class="job-title">Hospital Volunteer</div>
                <div class="company">Banner University Medical Center Phoenix</div>
                <div class="date-location">September 2024 - Present | Phoenix, AZ</div>
                <ul class="description">
                    <li>Assist with patient transport and provide comfort to patients and families</li>
                    <li>Help with administrative tasks and maintain clean patient areas</li>
                    <li>Observe healthcare professionals in various departments</li>
                    <li>Complete 4 hours per week of volunteer service</li>
                </ul>
            </div>

            <div class="experience-item">
                <div class="job-title">Community Health Fair Volunteer</div>
                <div class="company">Tempe Community Center</div>
                <div class="date-location">March 2024, October 2024 | Tempe, AZ</div>
                <ul class="description">
                    <li>Assisted with health screenings and educational booths</li>
                    <li>Helped register participants and distribute health information</li>
                    <li>Gained exposure to public health initiatives and community outreach</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Leadership & Activities</div>
            <div class="experience-item">
                <div class="job-title">Member</div>
                <div class="company">ASU Pre-Health Society</div>
                <div class="date-location">August 2023 - Present | Tempe, AZ</div>
                <ul class="description">
                    <li>Attend monthly meetings and guest speaker events</li>
                    <li>Participate in group volunteer activities at local healthcare facilities</li>
                    <li>Network with peers interested in health sciences careers</li>
                </ul>
            </div>

            <div class="experience-item">
                <div class="job-title">Study Group Leader</div>
                <div class="company">General Biology Study Group</div>
                <div class="date-location">September 2024 - Present | Tempe, AZ</div>
                <ul class="description">
                    <li>Organize weekly study sessions for General Biology II</li>
                    <li>Help classmates understand complex biological concepts</li>
                    <li>Create study materials and practice quizzes</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Technical Skills</div>
            <div class="skills-grid">
                <div class="skill-category">
                    <div class="skill-category-title">Laboratory Techniques:</div>
                    <div class="skill-list">
                        Basic Microscopy, Pipetting, Solution Preparation, Lab Safety Protocols
                    </div>
                </div>
                <div class="skill-category">
                    <div class="skill-category-title">Software & Analysis:</div>
                    <div class="skill-list">
                        Microsoft Office Suite, Basic Statistics, Google Workspace, Canvas LMS
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Honors & Awards</div>
            <ul class="awards-list">
                <li><strong>Dean's List</strong> - Spring 2024</li>
                <li><strong>ASU Merit Scholarship</strong> - Partial tuition scholarship recipient (2024)</li>
            </ul>
        </div>


        <div class="section">
            <div class="section-title">Languages</div>
            <div class="skill-list">
                <strong>English:</strong> Native proficiency<br>
                <strong>Mandarin Chinese:</strong> Conversational proficiency<br>
                <strong>Spanish:</strong> Basic proficiency
            </div>
        </div>
    </div>
</body>
</html>
