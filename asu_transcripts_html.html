<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ASU官方成绩单模板</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Times+New+Roman:wght@400;700&display=swap');
        
        body {
            font-family: 'Times New Roman', serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            color: #000;
            line-height: 1.4;
        }
        
        .transcript-container {
            max-width: 8.5in;
            width: 100%;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.15);
            position: relative;
            min-height: 11in;
        }
        
        /* 防伪水印效果 */
        .transcript-container::before {
            content: "ARIZONA STATE UNIVERSITY";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 48px;
            color: rgba(139, 21, 56, 0.03);
            font-weight: bold;
            white-space: nowrap;
            z-index: 0;
            pointer-events: none;
        }
        
        .transcript-content {
            position: relative;
            z-index: 1;
            background: white;
            padding: 0.75in;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #8B1538;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .university-logo {
            width: 140px;
            height: 45px;
            display: inline-block;
            margin-bottom: 15px;
            font-family: 'Arial Black', Arial, sans-serif;
        }
        
        .asu-sunburst {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            position: relative;
        }
        
        .asu-letter {
            font-size: 36px;
            font-weight: 900;
            color: #8B1538;
            position: relative;
            margin: 0 -2px;
        }
        
        .asu-letter.s {
            position: relative;
        }
        
        .sun-center {
            position: absolute;
            width: 18px;
            height: 18px;
            background: radial-gradient(circle, #FFC627 0%, #FFB000 100%);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 2;
        }
        
        .university-name {
            font-size: 24px;
            font-weight: bold;
            color: #8B1538;
            margin: 10px 0;
            letter-spacing: 2px;
        }
        
        .transcript-title {
            font-size: 20px;
            font-weight: bold;
            color: #000;
            margin: 10px 0;
            letter-spacing: 1px;
        }
        
        .official-notice {
            font-size: 12px;
            color: #8B1538;
            font-weight: bold;
            margin-top: 10px;
        }
        
        .student-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 25px;
            font-size: 12px;
        }
        
        .info-group h3 {
            font-size: 14px;
            font-weight: bold;
            color: #8B1538;
            margin: 0 0 10px 0;
            border-bottom: 1px solid #8B1538;
            padding-bottom: 3px;
        }
        
        .info-row {
            display: flex;
            margin: 6px 0;
        }
        
        .info-label {
            font-weight: bold;
            min-width: 120px;
            color: #000;
        }
        
        .info-value {
            flex: 1;
            color: #000;
        }
        
        .academic-summary {
            background: #f8f9fa;
            border: 2px solid #8B1538;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .academic-summary h3 {
            font-size: 16px;
            font-weight: bold;
            color: #8B1538;
            margin: 0 0 10px 0;
            text-align: center;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            font-size: 12px;
        }
        
        .academic-record {
            margin: 25px 0;
        }
        
        .academic-record h3 {
            font-size: 16px;
            font-weight: bold;
            color: #8B1538;
            margin: 0 0 15px 0;
            text-align: center;
            border-bottom: 2px solid #8B1538;
            padding-bottom: 5px;
        }
        
        .semester {
            margin-bottom: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .semester-header {
            background: #8B1538;
            color: white;
            padding: 8px 15px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .course-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
        }
        
        .course-table th {
            background: #f1f1f1;
            font-weight: bold;
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            font-size: 10px;
        }
        
        .course-table td {
            padding: 6px 8px;
            border-bottom: 1px solid #eee;
            vertical-align: top;
        }
        
        .course-table tr:nth-child(even) {
            background: #fafafa;
        }
        
        .semester-summary {
            background: #e9ecef;
            padding: 8px 15px;
            font-weight: bold;
            font-size: 11px;
            text-align: right;
        }
        
        .degree-section {
            background: #f8f9fa;
            border: 2px solid #8B1538;
            padding: 20px;
            margin: 25px 0;
            border-radius: 5px;
        }
        
        .degree-section h3 {
            font-size: 16px;
            font-weight: bold;
            color: #8B1538;
            margin: 0 0 15px 0;
            text-align: center;
        }
        
        .degree-info {
            text-align: center;
            font-size: 14px;
        }
        
        .degree-title {
            font-size: 16px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .transfer-credits {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #8B1538;
        }
        
        .transfer-credits h4 {
            font-size: 14px;
            font-weight: bold;
            color: #8B1538;
            margin: 0 0 10px 0;
        }
        
        .footer {
            margin-top: 40px;
            border-top: 3px solid #8B1538;
            padding-top: 20px;
        }
        
        .official-statement {
            text-align: center;
            font-size: 12px;
            font-weight: bold;
            color: #8B1538;
            margin-bottom: 20px;
            padding: 10px;
            border: 2px solid #8B1538;
            background: #f8f9fa;
        }
        
        .signature-area {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-top: 20px;
        }
        
        .signature-section {
            text-align: center;
            position: relative;
        }
        
        .signature-line {
            width: 200px;
            border-bottom: 2px solid #333;
            margin: 20px 0 8px 0;
            position: relative;
        }
        
        .signature-text {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            font-family: 'Brush Script MT', cursive;
            font-size: 24px;
            color: #1a365d;
            font-weight: bold;
            white-space: nowrap;
        }
        
        .signature-label {
            font-size: 10px;
            color: #666;
        }
        
        .signature-title {
            font-size: 9px;
            color: #666;
            margin-top: 3px;
        }
        
        .official-seal {
            width: 120px;
            height: 120px;
            border: 5px solid #8B1538;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            font-weight: bold;
            text-align: center;
            color: #8B1538;
            position: relative;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            box-shadow: inset 0 0 15px rgba(139, 21, 56, 0.1);
        }
        
        .official-seal::before {
            content: "";
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 3px solid #8B1538;
            border-radius: 50%;
            background: repeating-conic-gradient(
                from 0deg,
                #8B1538 0deg 6deg,
                transparent 6deg 12deg
            );
            z-index: -1;
        }
        
        .seal-text {
            line-height: 1.1;
            margin: 1px 0;
        }
        
        .seal-pitchfork {
            font-size: 16px;
            color: #FFC627;
            margin: 2px 0;
        }
        
        .seal-year {
            font-size: 6px;
            color: #8B1538;
            margin-top: 2px;
        }
        
        .print-info {
            text-align: center;
            font-size: 10px;
            color: #666;
            margin-top: 15px;
        }
        
        .page-number {
            position: absolute;
            bottom: 0.5in;
            right: 0.75in;
            font-size: 10px;
            color: #666;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .transcript-container {
                box-shadow: none;
                max-width: none;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="transcript-container">
        <div class="transcript-content">
            <!-- Header -->
            <div class="header">
                <div class="university-logo">
                    <div class="asu-sunburst">
                        <span class="asu-letter a">A</span>
                        <span class="asu-letter s">S
                            <div class="sun-center"></div>
                        </span>
                        <span class="asu-letter u">U</span>
                    </div>
                </div>
                <div class="university-name">ARIZONA STATE UNIVERSITY</div>
                <div class="transcript-title">OFFICIAL TRANSCRIPT</div>
                 </div>
            
            <!-- Student Information -->
            <div class="student-info">
                <div class="info-group">
                    <h3>Student Information</h3>
                    <div class="info-row">
                        <span class="info-label">Name:</span>
                        <span class="info-value">BROWN, JACK</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Student ID:</span>
                        <span class="info-value">235562311</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Date of Birth:</span>
                        <span class="info-value">May 5, 2005</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Address:</span>
                        <span class="info-value">456 Mill Avenue<br>Tempe, AZ 85281</span>
                    </div>
                </div>
                
                <div class="info-group">
                    <h3>Academic Information</h3>
                    <div class="info-row">
                        <span class="info-label">First Enrolled:</span>
                        <span class="info-value">Fall 2023</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">College:</span>
                        <span class="info-value">Ira A. Fulton Schools of Engineering</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Campus:</span>
                        <span class="info-value">Tempe</span>
                    </div>
                    
                </div>
            </div>
            
            <!-- Academic Summary -->
            <div class="academic-summary">
                <h3>ACADEMIC SUMMARY</h3>
                <div class="summary-grid">
                    <div>
                        <div class="info-row">
                            <span class="info-label">Program:</span>
                            <span class="info-value">Bachelor of Science</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Major:</span>
                            <span class="info-value">Computer Science</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Academic Status:</span>
                            <span class="info-value"><strong>Enrolled - Sophomore</strong></span>
                        </div>
                    </div>
                    <div>
                        <div class="info-row">
                            <span class="info-label">Cumulative GPA:</span>
                            <span class="info-value"><strong>3.58/4.00</strong></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Total Credits Earned:</span>
                            <span class="info-value">63.00</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">ASU Credits:</span>
                            <span class="info-value">63.00</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Academic Record -->
            <div class="academic-record">
                <h3>ACADEMIC RECORD</h3>
                
                <!-- Fall 2023 -->
                <div class="semester">
                    <div class="semester-header">FALL 2023</div>
                    <table class="course-table">
                        <thead>
                            <tr>
                                <th>Course</th>
                                <th>Course Title</th>
                                <th>Credits</th>
                                <th>Grade</th>
                                <th>Quality Points</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>CSE 110</td>
                                <td>Introduction to Programming</td>
                                <td>4.00</td>
                                <td>B+</td>
                                <td>13.32</td>
                            </tr>
                            <tr>
                                <td>MAT 265</td>
                                <td>Calculus for Engineers I</td>
                                <td>4.00</td>
                                <td>A-</td>
                                <td>14.68</td>
                            </tr>
                            <tr>
                                <td>ENG 101</td>
                                <td>First-Year Composition</td>
                                <td>3.00</td>
                                <td>B</td>
                                <td>9.00</td>
                            </tr>
                            <tr>
                                <td>UNI 100</td>
                                <td>University Success</td>
                                <td>1.00</td>
                                <td>P</td>
                                <td>0.00</td>
                            </tr>
                            <tr>
                                <td>CHM 113</td>
                                <td>General Chemistry I</td>
                                <td>4.00</td>
                                <td>B-</td>
                                <td>10.68</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="semester-summary">
                        Term Credits: 16.00 | Quality Points: 47.68 | Term GPA: 3.48 | Cumulative GPA: 3.48
                    </div>
                </div>
                
                <!-- Spring 2024 -->
                <div class="semester">
                    <div class="semester-header">SPRING 2024</div>
                    <table class="course-table">
                        <thead>
                            <tr>
                                <th>Course</th>
                                <th>Course Title</th>
                                <th>Credits</th>
                                <th>Grade</th>
                                <th>Quality Points</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>CSE 205</td>
                                <td>Object-Oriented Programming</td>
                                <td>3.00</td>
                                <td>A</td>
                                <td>12.00</td>
                            </tr>
                            <tr>
                                <td>MAT 266</td>
                                <td>Calculus for Engineers II</td>
                                <td>4.00</td>
                                <td>B+</td>
                                <td>13.32</td>
                            </tr>
                            <tr>
                                <td>PHY 121</td>
                                <td>University Physics I</td>
                                <td>4.00</td>
                                <td>A-</td>
                                <td>14.68</td>
                            </tr>
                            <tr>
                                <td>ENG 102</td>
                                <td>First-Year Composition</td>
                                <td>3.00</td>
                                <td>B+</td>
                                <td>9.99</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="semester-summary">
                        Term Credits: 14.00 | Quality Points: 49.99 | Term GPA: 3.57 | Cumulative GPA: 3.52
                    </div>
                </div>
                
                <!-- Fall 2024 -->
                <div class="semester">
                    <div class="semester-header">FALL 2024</div>
                    <table class="course-table">
                        <thead>
                            <tr>
                                <th>Course</th>
                                <th>Course Title</th>
                                <th>Credits</th>
                                <th>Grade</th>
                                <th>Quality Points</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>CSE 240</td>
                                <td>Introduction to Programming Languages</td>
                                <td>3.00</td>
                                <td>B+</td>
                                <td>9.99</td>
                            </tr>
                            <tr>
                                <td>MAT 267</td>
                                <td>Calculus for Engineers III</td>
                                <td>4.00</td>
                                <td>A</td>
                                <td>16.00</td>
                            </tr>
                            <tr>
                                <td>PHY 131</td>
                                <td>University Physics II</td>
                                <td>4.00</td>
                                <td>B</td>
                                <td>12.00</td>
                            </tr>
                            <tr>
                                <td>STP 226</td>
                                <td>Elements of Statistics</td>
                                <td>3.00</td>
                                <td>A-</td>
                                <td>11.01</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="semester-summary">
                        Term Credits: 14.00 | Quality Points: 49.00 | Term GPA: 3.50 | Cumulative GPA: 3.51
                    </div>
                </div>
                
                <!-- Spring 2025 -->
                <div class="semester">
                    <div class="semester-header">SPRING 2025</div>
                    <table class="course-table">
                        <thead>
                            <tr>
                                <th>Course</th>
                                <th>Course Title</th>
                                <th>Credits</th>
                                <th>Grade</th>
                                <th>Quality Points</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>CSE 310</td>
                                <td>Data Structures and Algorithms</td>
                                <td>3.00</td>
                                <td>A</td>
                                <td>12.00</td>
                            </tr>
                            <tr>
                                <td>CSE 230</td>
                                <td>Computer Organization</td>
                                <td>4.00</td>
                                <td>A-</td>
                                <td>14.68</td>
                            </tr>
                            <tr>
                                <td>MAT 274</td>
                                <td>Elementary Differential Equations</td>
                                <td>3.00</td>
                                <td>B+</td>
                                <td>9.99</td>
                            </tr>
                            <tr>
                                <td>HIS 103</td>
                                <td>World History</td>
                                <td>3.00</td>
                                <td>A</td>
                                <td>12.00</td>
                            </tr>
                            <tr>
                                <td>COM 263</td>
                                <td>Elements of Intercultural Communication</td>
                                <td>3.00</td>
                                <td>B+</td>
                                <td>9.99</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="semester-summary">
                        Term Credits: 16.00 | Quality Points: 58.66 | Term GPA: 3.67 | Cumulative GPA: 3.55
                    </div>
                </div>
                
                <!-- Summer 2025 -->
                <div class="semester">
                    <div class="semester-header">SUMMER 2025</div>
                    <table class="course-table">
                        <thead>
                            <tr>
                                <th>Course</th>
                                <th>Course Title</th>
                                <th>Credits</th>
                                <th>Grade</th>
                                <th>Quality Points</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>CSE 340</td>
                                <td>Introduction to Compilers</td>
                                <td>3.00</td>
                                <td>A-</td>
                                <td>11.01</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="semester-summary">
                        Term Credits: 3.00 | Quality Points: 11.01 | Term GPA: 3.67 | Cumulative GPA: 3.58
                    </div>
                </div>
            </div>
            
            <!-- Academic Status -->
            <div class="degree-section">
                <h3>CURRENT ACADEMIC STATUS</h3>
                <div class="degree-info">
                    <div class="degree-title">Bachelor of Science in Computer Science</div>
                    <div style="font-size: 14px; margin: 10px 0;">
                        <strong>Program Status:</strong> Enrolled - Rising Junior
                    </div>
                    <div style="font-size: 14px; margin: 10px 0;">
                        <strong>Expected Graduation:</strong> Spring 2027
                    </div>
                    <div style="font-size: 12px; color: #666;">
                        Ira A. Fulton Schools of Engineering<br>
                        Arizona State University, Tempe Campus
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="footer">
                <div class="signature-area">
                    <div class="signature-section">
                        <div class="signature-line">
                            <div class="signature-text">Dr. Susan M. Martinez</div>
                        </div>
                        <div class="signature-label">Dr. Susan M. Martinez</div>
                        <div class="signature-title">University Registrar</div>
                        <div class="signature-title">Arizona State University</div>
                    </div>
                    
                    <div class="official-seal">
                        <div class="seal-text" style="font-size: 7px; font-weight: 900;">ARIZONA STATE</div>
                        <div class="seal-text" style="font-size: 7px; font-weight: 900;">UNIVERSITY</div>
                        <div class="seal-pitchfork">🔱</div>
                        <div class="seal-text" style="font-size: 5px; margin: 2px 0; background: #8B1538; color: white; padding: 2px 4px; border-radius: 3px; font-weight: bold;">DITAT DEUS</div>
                        <div class="seal-text" style="font-size: 6px; font-weight: bold;">TEMPE ARIZONA</div>
                        <div class="seal-year">EST. 1885</div>
                    </div>
                </div>
                
                <div class="print-info">
                    Transcript Date: August 1, 2025<br>
                    Order Number: TR-2025-080401-001<br>
                    Authentication Code: ASU-8B15-3847-9C2D
                </div>
        </div>
    </div>
</body>
</html>